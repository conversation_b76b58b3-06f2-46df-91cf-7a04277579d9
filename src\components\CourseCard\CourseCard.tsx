import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { HeartIcon } from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";

interface Course {
  id: string;
  name: string;
  trainer: string;
  description: string;
  image: string;
  status: "owned" | "subscribed" | "refund-requested" | "refunded";
  expiresIn?: string;
  refundStatus?: "pending" | "approved" | "rejected" | "processed";
  isFavorite?: boolean;
  // New properties for enrollment actions
  enrollmentId?: number;
  canRefund?: boolean;
  billingFailed?: boolean;
  paymentType?: "one_time" | "subscription";
}

interface CourseCardProps {
  course: Course;
  onView?: (courseId: string) => void;
  onRefund?: (courseId: string) => void;
  onPay?: (courseId: string) => void;
  onFavoriteToggle?: (courseId: string, isFavorite: boolean) => void;
  onViewRefundStatus?: (enrollmentId: number) => void;
  onViewRefundHistory?: () => void;
}

const CourseCard = ({
  course,
  onView,
  onRefund,
  onPay,
  onFavoriteToggle,
  onViewRefundStatus,
  onViewRefundHistory,
}: CourseCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isFavorite, setIsFavorite] = useState(course.isFavorite || false);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newFavoriteState = !isFavorite;
    setIsFavorite(newFavoriteState);
    onFavoriteToggle?.(course.id, newFavoriteState);
  };

  const handleViewClick = () => {
    // Use enrollmentId for navigation, fallback to course.id for compatibility
    const idToUse = course.enrollmentId?.toString() || course.id;
    onView?.(idToUse);
  };

  const handleRefundClick = () => {
    onRefund?.(course.id);
  };

  const handlePayClick = () => {
    onPay?.(course.id);
  };

  const handleViewRefundStatusClick = () => {
    if (course.enrollmentId) {
      onViewRefundStatus?.(course.enrollmentId);
    }
  };

  const handleViewRefundHistoryClick = () => {
    onViewRefundHistory?.();
  };

  const renderActionButtons = () => {
    switch (course.status) {
      case "owned":
        return (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2 items-center justify-end">
              <div>
                <button
                  onClick={handleViewRefundStatusClick}
                  className="px-3 py-1 rounded text-xs text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors duration-200"
                >
                  Refund Status
                </button>
                {course.canRefund && (
                  <button
                    onClick={handleRefundClick}
                    className="px-4 py-2 rounded border border-primary bg-white text-primary text-sm font-semibold hover:bg-green-50 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    Refund
                  </button>
                )}
              </div>
              <button
                onClick={handleViewClick}
                className="px-4 py-2 rounded bg-primary text-white text-sm font-semibold hover:bg-green-500 transition-colors duration-200"
              >
                View
              </button>
            </div>
            {course.enrollmentId && (
              <div className="flex gap-2 justify-end">
                <button
                  onClick={handleViewRefundHistoryClick}
                  className="px-3 py-1 hidden rounded text-xs text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors duration-200"
                >
                  Refund History
                </button>
              </div>
            )}
          </div>
        );

      case "subscribed":
        return (
          <div className="flex justify-between items-center gap-2">
            {course.billingFailed && (
              <div className="text-red-500 text-xs">Expires in</div>
            )}
            <div className="flex gap-2 grow justify-end">
              {course.billingFailed && (
                <button
                  onClick={handlePayClick}
                  className="px-4 py-2 rounded border border-primary bg-white text-primary text-sm font-semibold hover:bg-green-50 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200"
                >
                  Pay
                </button>
              )}
              <button
                onClick={handleViewClick}
                className="px-4 py-2 rounded bg-primary text-white text-sm font-semibold hover:bg-green-500 transition-colors duration-200"
              >
                View
              </button>
            </div>
          </div>
        );

      case "refund-requested":
        const getRefundStatusColors = () => {
          switch (course.refundStatus) {
            case "approved":
              return "border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-800";
            case "rejected":
              return "border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900 hover:bg-red-100 dark:hover:bg-red-800";
            case "processed":
              return "border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900 hover:bg-green-100 dark:hover:bg-green-800";
            default:
              return "border-primary dark:border-yellow-500 bg-primary-disabled dark:bg-yellow-500/0 hover:bg-primary/10 dark:hover:bg-yellow-500/10";
          }
        };

        const getRefundStatusTextColor = () => {
          switch (course.refundStatus) {
            case "approved":
              return "text-blue-800 dark:text-blue-200";
            case "rejected":
              return "text-red-800 dark:text-red-200";
            case "processed":
              return "text-green-800 dark:text-green-200";
            default:
              return "text-primary dark:text-[#eab308]";
          }
        };

        const getRefundStatusDotColor = () => {
          switch (course.refundStatus) {
            case "approved":
              return "bg-blue-600 dark:bg-blue-400";
            case "rejected":
              return "bg-red-600 dark:bg-red-400";
            case "processed":
              return "bg-green-600 dark:bg-green-400";
            default:
              return "bg-primary dark:bg-yellow-500";
          }
        };

        return (
          <div className="w-full">
            <button
              onClick={handleViewRefundStatusClick}
              className={`w-full p-3 rounded-md border transition-colors duration-200 ${getRefundStatusColors()}`}
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-3 h-3 rounded-full opacity-60 ${getRefundStatusDotColor()}`}
                ></div>
                <span
                  className={`text-sm font-medium transition-colors duration-200 ${getRefundStatusTextColor()}`}
                >
                  Refund Status:{" "}
                  {course.refundStatus === "pending"
                    ? "Pending Review"
                    : course.refundStatus === "approved"
                      ? "Approved"
                      : course.refundStatus === "rejected"
                        ? "Rejected"
                        : course.refundStatus === "processed"
                          ? "Processed"
                          : "Pending"}
                </span>
                <div className="ml-auto">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="16"
                    viewBox="0 0 12 16"
                    fill="none"
                  >
                    <g clip-path="url(#clip0_95_3027)">
                      <path
                        d="M1 0C0.446875 0 0 0.446875 0 1C0 1.55313 0.446875 2 1 2V2.34375C1 3.66875 1.52813 4.94063 2.46563 5.87813L4.58437 8L2.46563 10.1219C1.52813 11.0594 1 12.3313 1 13.6562V14C0.446875 14 0 14.4469 0 15C0 15.5531 0.446875 16 1 16H2H10H11C11.5531 16 12 15.5531 12 15C12 14.4469 11.5531 14 11 14V13.6562C11 12.3313 10.4719 11.0594 9.53438 10.1219L7.41563 8L9.5375 5.87813C10.475 4.94063 11.0031 3.66875 11.0031 2.34375V2C11.5563 2 12.0031 1.55313 12.0031 1C12.0031 0.446875 11.5563 0 11.0031 0H10H2H1ZM3 2.34375V2H9V2.34375C9 2.9375 8.825 3.5125 8.5 4H3.5C3.17812 3.5125 3 2.9375 3 2.34375ZM3.5 12C3.60938 11.8344 3.7375 11.6781 3.87812 11.5344L6 9.41562L8.12187 11.5375C8.26562 11.6813 8.39062 11.8375 8.5 12.0031H3.5V12Z"
                        fill={mode == "dark" ? "#eab308" : "#4CBF6D"}
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_95_3027">
                        <path d="M0 0H12V16H0V0Z" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
            </button>
            <div className="mt-2 flex gap-2 justify-center">
              <button
                onClick={handleViewRefundHistoryClick}
                className="px-3 py-1 rounded text-xs text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors duration-200"
              >
                View History
              </button>
            </div>
          </div>
        );

      case "refunded":
        return (
          <div className="w-full">
            <div className="w-full p-3 rounded-md border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900 transition-colors duration-200">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-600 dark:bg-green-400 rounded-full"></div>
                <span className="text-sm font-medium transition-colors text-green-800 dark:text-green-200 duration-200">
                  Refund Completed
                </span>
                <div className="ml-auto">
                  <svg
                    className="w-4 h-4 text-green-600 dark:text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <div className="mt-2 flex gap-2 justify-center">
              <button
                onClick={handleViewRefundHistoryClick}
                className="px-3 py-1 rounded text-xs text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors duration-200"
              >
                View History
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className="w-full flex flex-col max-w-sm md:max-w-none lg:w-96 rounded-md shadow-lg border transition-all duration-200 hover:shadow-xl cursor-pointer flex-shrink-0 mx-auto lg:mx-0"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Image Container */}
      <div className="relative">
        <div className="w-full h-48 rounded-t-md overflow-hidden bg-gray-100 dark:bg-gray-800">
          <img
            src={course.image}
            alt={course.name}
            className="w-full h-full object-cover transition-transform duration-200 hover:scale-105"
          />
        </div>

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteClick}
          className="absolute top-2 right-2 w-4 h-6 flex items-center justify-center"
        >
          {isFavorite ? (
            <HeartIcon className="w-4 h-4 text-white" />
          ) : (
            <HeartOutlineIcon className="w-4 h-4 text-white" />
          )}
        </button>
      </div>

      {/* Content */}
      <div className="p-4 flex grow flex-col">
        {/* Course Name */}
        <h3
          className="text-base font-semibold mb-1 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          {course.name}
        </h3>

        {/* Trainer */}
        <p
          className="text-sm mb-3 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          by {course.trainer}
        </p>

        {/* Description */}
        <div className="grow">
          <p
            className="text-sm mb-6 line-clamp-3 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {course.description}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="mt-auto w-full">{renderActionButtons()}</div>
      </div>
    </div>
  );
};

export default CourseCard;
