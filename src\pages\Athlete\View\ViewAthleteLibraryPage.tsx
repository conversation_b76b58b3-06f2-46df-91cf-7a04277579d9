import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { CourseCard } from "@/components/CourseCard";
import { LibrarySection } from "@/components/LibrarySection";
import { TrainersLibraryGrid } from "@/components/TrainersLibraryGrid";
import { ProgramsLibraryGrid } from "@/components/ProgramsLibraryGrid";
import { useAthleteEnrollments } from "@/hooks/useAthleteEnrollments";
import {
  useAthleteFavoritePrograms,
  useAthleteFavoriteTrainers,
  useAthleteProgramFavorites,
  useAthleteTrainerFavorites,
} from "@/hooks/useAthleteFavorites";
import {
  EnrollmentItem,
  FavoriteProgramItem,
  FavoriteTrainerItem,
} from "@/interfaces/model.interface";
import { LoadingSkeleton } from "@/components/LoadingSkeleton";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import {
  RefundRequestModal,
  RefundStatusModal,
  RefundHistoryModal,
} from "@/components/Athlete";
import { ActionConfirmationModal } from "@/components/ActionConfirmationModal";

// Data transformation functions
const transformEnrollmentToCourse = (enrollment: EnrollmentItem) => ({
  id: enrollment.id.toString(),
  name: enrollment.program.name,
  trainer: enrollment.trainer.full_name,
  description: enrollment.program.description,
  image: enrollment.program.image_url || "https://placehold.co/366x192",
  status:
    enrollment.category === "owned"
      ? ("owned" as const)
      : enrollment.category === "subscribed"
        ? ("subscribed" as const)
        : enrollment.category === "pending_refund"
          ? ("refund-requested" as const)
          : enrollment.category === "refunded"
            ? ("refunded" as const)
            : ("owned" as const), // fallback
  expiresIn: enrollment.subscription_info?.billing_failed
    ? "Expires soon"
    : undefined,
  refundStatus:
    enrollment.category === "pending_refund"
      ? (enrollment.refund_info?.status as
          | "pending"
          | "approved"
          | "rejected"
          | "processed") || "pending"
      : enrollment.category === "refunded"
        ? ("processed" as const)
        : undefined,
  isFavorite: false, // This would need to be determined from favorites data
  // Add enrollment-specific data for actions
  enrollmentId: enrollment.id,
  canRefund: enrollment.refund_info?.can_request || false,
  billingFailed: enrollment.subscription_info?.billing_failed || false,
  paymentType: enrollment.payment_type,
});

const transformFavoriteTrainerToTrainer = (trainer: FavoriteTrainerItem) => ({
  id: trainer.id.toString(),
  name: trainer.full_name,
  description: trainer.bio,
  image: trainer.photo || "https://placehold.co/366x192",
  rating: trainer.average_rating,
  startingPrice: 0, // This would need to be calculated from trainer's programs
  isFavorite: true, // Always true since these are favorites
});

const transformFavoriteProgramToProgram = (program: FavoriteProgramItem) => ({
  id: program.id.toString(),
  name: program.name,
  description: program.description,
  price: program.price,
  rating: program.average_rating,
  image: program.image_url || "https://placehold.co/256x144",
  isFavorite: true, // Always true since these are favorites
});

const ViewAthleteLibraryPage = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();

  // Modal states
  const [refundRequestModal, setRefundRequestModal] = useState<{
    isOpen: boolean;
    enrollmentId?: number;
    programName?: string;
    amount?: number;
    currency?: string;
  }>({ isOpen: false });

  const [refundStatusModal, setRefundStatusModal] = useState<{
    isOpen: boolean;
    enrollmentId?: number;
  }>({ isOpen: false });

  const [refundHistoryModal, setRefundHistoryModal] = useState(false);

  const [cancelSubscriptionModal, setCancelSubscriptionModal] = useState<{
    isOpen: boolean;
    enrollmentId?: number;
    programName?: string;
  }>({ isOpen: false });

  // API hooks
  const {
    data: enrollmentsData,
    isLoading: enrollmentsLoading,
    error: enrollmentsError,
    refetch: refetchEnrollments,
  } = useAthleteEnrollments();

  const {
    data: favoritePrograms,
    isLoading: favoriteProgramsLoading,
    error: favoriteProgramsError,
    refetch: refetchFavoritePrograms,
  } = useAthleteFavoritePrograms();

  const {
    data: favoriteTrainers,
    isLoading: favoriteTrainersLoading,
    error: favoriteTrainersError,
    refetch: refetchFavoriteTrainers,
  } = useAthleteFavoriteTrainers();

  const { toggleProgramFavorite } = useAthleteProgramFavorites();
  const { toggleTrainerFavorite } = useAthleteTrainerFavorites();

  // Event handlers
  const handleCourseView = (courseId: string) => {
    // Navigate to enrolled program access page
    navigate(`/athlete/programs/enrolled/access?enrollment=${courseId}`);
  };

  const handleCourseRefund = (courseId: string) => {
    // Find the enrollment to get details for refund request
    const allEnrollments = [
      ...(enrollmentsData?.data?.owned || []),
      ...(enrollmentsData?.data?.subscribed || []),
    ];

    const enrollment = allEnrollments.find((e) => e.id.toString() === courseId);

    if (enrollment) {
      setRefundRequestModal({
        isOpen: true,
        enrollmentId: enrollment.id,
        programName: enrollment.program.name,
        amount: enrollment.amount || 0,
        currency: enrollment.currency || "USD",
      });
    }
  };

  const handleCoursePay = (courseId: string) => {
    // TODO: Implement payment functionality for failed billing
    // This will be implemented later
    void courseId;
  };

  const handleCourseCancelSubscription = (courseId: string) => {
    // Find the enrollment to get details for cancel subscription
    const allEnrollments = [
      ...(enrollmentsData?.data?.owned || []),
      ...(enrollmentsData?.data?.subscribed || []),
    ];

    const enrollment = allEnrollments.find((e) => e.id.toString() === courseId);

    if (enrollment) {
      setCancelSubscriptionModal({
        isOpen: true,
        enrollmentId: enrollment.id,
        programName: enrollment.program.name,
      });
    }
  };

  const handleCourseFavoriteToggle = (
    courseId: string,
    isFavorite: boolean
  ) => {
    // TODO: Implement course favorite toggle (this would be program favorites)
    void courseId;
    void isFavorite;
  };

  const handleTrainerFavoriteToggle = async (
    trainerId: string,
    isFavorite: boolean
  ) => {
    try {
      await toggleTrainerFavorite(trainerId, isFavorite);
    } catch (error) {
      // Error is handled by the mutation hook's onError callback
      void error;
    }
  };

  const handleProgramFavoriteToggle = async (
    programId: string,
    isFavorite: boolean
  ) => {
    try {
      await toggleProgramFavorite(programId, isFavorite);
    } catch (error) {
      // Error is handled by the mutation hook's onError callback
      void error;
    }
  };

  // Refund modal handlers
  const handleViewRefundStatus = (enrollmentId: number) => {
    setRefundStatusModal({
      isOpen: true,
      enrollmentId,
    });
  };

  const handleViewRefundHistory = () => {
    setRefundHistoryModal(true);
  };

  const handleRefundRequestSuccess = () => {
    // Refresh enrollments data after successful refund request
    refetchEnrollments();
  };

  const handleCancelSubscriptionSuccess = () => {
    // Refresh enrollments data after successful subscription cancellation
    refetchEnrollments();
    setCancelSubscriptionModal({ isOpen: false });
  };

  const handleRequestRefundFromStatus = () => {
    // Close status modal and open request modal
    const enrollmentId = refundStatusModal.enrollmentId;
    setRefundStatusModal({ isOpen: false });

    if (enrollmentId) {
      const allEnrollments = [
        ...(enrollmentsData?.data?.owned || []),
        ...(enrollmentsData?.data?.subscribed || []),
      ];

      const enrollment = allEnrollments.find((e) => e.id === enrollmentId);

      if (enrollment) {
        setRefundRequestModal({
          isOpen: true,
          enrollmentId: enrollment.id,
          programName: enrollment.program.name,
          amount: enrollment.amount || 0,
          currency: enrollment.currency || "USD",
        });
      }
    }
  };

  // Transform data
  const ownedCourses =
    enrollmentsData?.data?.owned?.map(transformEnrollmentToCourse) || [];
  const subscribedCourses =
    enrollmentsData?.data?.subscribed?.map(transformEnrollmentToCourse) || [];
  const pendingRefundCourses =
    enrollmentsData?.data?.pending_refund?.map(transformEnrollmentToCourse) ||
    [];
  const refundedCourses =
    enrollmentsData?.data?.refunded?.map(transformEnrollmentToCourse) || [];

  const trainers =
    favoriteTrainers?.data?.map(transformFavoriteTrainerToTrainer) || [];
  const programs =
    favoritePrograms?.data?.map(transformFavoriteProgramToProgram) || [];

  // Section error component
  const SectionError = ({
    title,
    error,
    onRetry,
  }: {
    title: string;
    error: any;
    onRetry: () => void;
  }) => (
    <div className="mb-12">
      <h2
        className="text-xl font-semibold mb-4"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        {title}
      </h2>
      <div className="border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900 rounded-lg p-6 text-center">
        <p className="text-red-800 dark:text-red-200 mb-4">
          Failed to load {title.toLowerCase()}.{" "}
          {error?.message || "Please try again."}
        </p>
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
        >
          Try Again
        </button>
      </div>
    </div>
  );

  // Section loading component
  const SectionLoading = ({ title }: { title: string }) => (
    <div className="mb-12">
      <h2
        className="text-xl font-semibold mb-4"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        {title}
      </h2>
      <LoadingSkeleton />
    </div>
  );

  return (
    <div
      className="w-full transition-colors duration-200 px-4 sm:px-6 lg:px-20"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
    >
      <div className="max-w-7xl mx-auto py-6 lg:py-12">
        {/* Page Title */}
        <h1
          className="text-2xl font-bold mb-8 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          Library
        </h1>

        {/* Enrollments Section */}
        {enrollmentsLoading ? (
          <SectionLoading title="My Enrollments" />
        ) : enrollmentsError ? (
          <SectionError
            title="My Enrollments"
            error={enrollmentsError}
            onRetry={refetchEnrollments}
          />
        ) : (
          <>
            {/* Owned Courses Section */}
            {ownedCourses.length > 0 && (
              <LibrarySection
                title="Owned"
                gridType="courses"
                className="mb-12"
              >
                {ownedCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onView={handleCourseView}
                    onRefund={handleCourseRefund}
                    onPay={handleCoursePay}
                    onFavoriteToggle={handleCourseFavoriteToggle}
                    onViewRefundStatus={handleViewRefundStatus}
                    onViewRefundHistory={handleViewRefundHistory}
                  />
                ))}
              </LibrarySection>
            )}

            {/* Subscribed Courses Section */}
            {subscribedCourses.length > 0 && (
              <LibrarySection
                title="Subscribed"
                gridType="courses"
                className="mb-12"
              >
                {subscribedCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onView={handleCourseView}
                    onRefund={handleCourseRefund}
                    onPay={handleCoursePay}
                    onFavoriteToggle={handleCourseFavoriteToggle}
                    onViewRefundStatus={handleViewRefundStatus}
                    onViewRefundHistory={handleViewRefundHistory}
                    onCancelSubscription={handleCourseCancelSubscription}
                  />
                ))}
              </LibrarySection>
            )}

            {/* Pending Refund Courses Section */}
            {pendingRefundCourses.length > 0 && (
              <LibrarySection
                title="Refund Requested"
                gridType="courses"
                className="mb-12"
              >
                {pendingRefundCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onView={handleCourseView}
                    onRefund={handleCourseRefund}
                    onPay={handleCoursePay}
                    onFavoriteToggle={handleCourseFavoriteToggle}
                    onViewRefundStatus={handleViewRefundStatus}
                    onViewRefundHistory={handleViewRefundHistory}
                  />
                ))}
              </LibrarySection>
            )}

            {/* Refunded Courses Section */}
            {refundedCourses.length > 0 && (
              <LibrarySection
                title="Refunded"
                gridType="courses"
                className="mb-12"
              >
                {refundedCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onView={handleCourseView}
                    onRefund={handleCourseRefund}
                    onPay={handleCoursePay}
                    onFavoriteToggle={handleCourseFavoriteToggle}
                    onViewRefundStatus={handleViewRefundStatus}
                    onViewRefundHistory={handleViewRefundHistory}
                  />
                ))}
              </LibrarySection>
            )}

            {/* Empty Enrollments State */}
            {!enrollmentsLoading &&
              !enrollmentsError &&
              ownedCourses.length === 0 &&
              subscribedCourses.length === 0 &&
              pendingRefundCourses.length === 0 &&
              refundedCourses.length === 0 && (
                <div className="text-center py-8 mb-12">
                  <p
                    className="text-lg mb-2"
                    style={{ color: THEME_COLORS[mode].TEXT }}
                  >
                    No enrollments yet
                  </p>
                  <p
                    className="text-sm opacity-70"
                    style={{ color: THEME_COLORS[mode].TEXT }}
                  >
                    Start exploring programs to build your fitness library!
                  </p>
                </div>
              )}
          </>
        )}

        {/* Favourite Trainers Section */}
        {favoriteTrainersLoading ? (
          <SectionLoading title="Favourite Trainers" />
        ) : favoriteTrainersError ? (
          <SectionError
            title="Favourite Trainers"
            error={favoriteTrainersError}
            onRetry={refetchFavoriteTrainers}
          />
        ) : (
          trainers.length > 0 && (
            <TrainersLibraryGrid
              trainers={trainers}
              onFavoriteToggle={handleTrainerFavoriteToggle}
            />
          )
        )}

        {/* Favourite Programs Section */}
        {favoriteProgramsLoading ? (
          <SectionLoading title="Favourite Programs" />
        ) : favoriteProgramsError ? (
          <SectionError
            title="Favourite Programs"
            error={favoriteProgramsError}
            onRetry={refetchFavoritePrograms}
          />
        ) : (
          programs.length > 0 && (
            <ProgramsLibraryGrid
              programs={programs}
              onFavoriteToggle={handleProgramFavoriteToggle}
            />
          )
        )}

        {/* Global Empty State - only show if all sections are loaded and empty */}
        {!enrollmentsLoading &&
          !favoriteProgramsLoading &&
          !favoriteTrainersLoading &&
          !enrollmentsError &&
          !favoriteProgramsError &&
          !favoriteTrainersError &&
          ownedCourses.length === 0 &&
          subscribedCourses.length === 0 &&
          pendingRefundCourses.length === 0 &&
          refundedCourses.length === 0 &&
          trainers.length === 0 &&
          programs.length === 0 && (
            <div className="text-center py-12">
              <p
                className="text-lg mb-4"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Your library is empty
              </p>
              <p
                className="text-sm opacity-70"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Start exploring programs and trainers to build your fitness
                library!
              </p>
            </div>
          )}

        {/* Refund Modals */}
        <RefundRequestModal
          isOpen={refundRequestModal.isOpen}
          onClose={() => setRefundRequestModal({ isOpen: false })}
          enrollmentId={refundRequestModal.enrollmentId!}
          programName={refundRequestModal.programName!}
          amount={refundRequestModal.amount!}
          currency={refundRequestModal.currency!}
          onSuccess={handleRefundRequestSuccess}
        />

        <RefundStatusModal
          isOpen={refundStatusModal.isOpen}
          onClose={() => setRefundStatusModal({ isOpen: false })}
          enrollmentId={refundStatusModal.enrollmentId!}
          onRequestRefund={handleRequestRefundFromStatus}
        />

        <RefundHistoryModal
          isOpen={refundHistoryModal}
          onClose={() => setRefundHistoryModal(false)}
        />

        {/* Cancel Subscription Modal */}
        <ActionConfirmationModal
          isOpen={cancelSubscriptionModal.isOpen}
          onClose={() => setCancelSubscriptionModal({ isOpen: false })}
          title="Cancel Subscription"
          action="Cancel Subscription"
          mode="custom"
          customMessage={`Are you sure you want to cancel your subscription to "${cancelSubscriptionModal.programName}"? This action cannot be undone.`}
          data={
            cancelSubscriptionModal.enrollmentId
              ? [{ id: cancelSubscriptionModal.enrollmentId }]
              : []
          }
          options={{
            endpoint: `/v2/api/kanglink/custom/enrollment/${cancelSubscriptionModal.enrollmentId}/cancel`,
            method: "POST",
            payload: {},
          }}
          onSuccess={handleCancelSubscriptionSuccess}
        />
      </div>
    </div>
  );
};

export default ViewAthleteLibraryPage;
